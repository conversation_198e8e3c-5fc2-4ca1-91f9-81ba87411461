# 檔名驅動架構說明文件

## 概述

本文件說明新的檔名驅動架構，用於取代原本基於時間的檔案處理方式。新架構根據檔案名稱自動識別檔案類型和通路，並觸發對應的處理流程。

## 問題背景

原本的系統使用時間排程來下載和處理檔案，但存在以下問題：
- 檔案不一定在預定時間可用
- 伺服器延遲導致下載到錯誤批次的檔案
- 造成連鎖的工作失敗

## 解決方案

新的檔名驅動架構：
- 根據檔案名稱自動識別檔案類型
- 支援自動檔案監控和處理
- 保持向後相容性

## 檔案名稱格式

### 格式規範
```
F787$YYYYMMDD[0-9]{2}O
```

### 通路和交易類型對應

| 通路 | 每日交易 | 補交易 | 健康食品交易 |
|------|----------|--------|--------------|
| CLINICO | 01 | 04 | 21 |
| SKD | 02 | 05 | 22 |
| IB | 03 | 06 | 23 |

### 範例檔案名稱
- `F787$2024072601O` - CLINICO 每日交易 (2024/07/26)
- `F787$2024072622O` - SKD 健康食品交易 (2024/07/26)
- `F787$2024072606O` - IB 補交易 (2024/07/26)

## 核心組件

### 1. FileNameParser
檔案名稱解析器，負責：
- 解析檔案名稱格式
- 識別通路類型
- 識別交易類型
- 驗證日期有效性

```typescript
import { FileNameParser } from '../services/parsers/fileName.parser';

const result = FileNameParser.parse('F787$2024072601O');
if (result.isValid) {
    console.log(result.company);        // 'CLINICO'
    console.log(result.transactionType); // 'DAILY'
    console.log(result.fileDate);       // Date object
}
```

### 2. FileProcessingStrategy
檔案處理策略模式，包含：
- `DailyTransactionStrategy` - 每日交易處理
- `RepayTransactionStrategy` - 補交易處理
- `FoodTransactionStrategy` - 健康食品交易處理

```typescript
import { FileProcessingStrategyFactory } from '../services/strategies/fileProcessing.strategy';

const strategy = FileProcessingStrategyFactory.createStrategy(
    TransactionType.DAILY,
    'CLINICO'
);
const result = await strategy.process(parseResult, filePath);
```

### 3. FileMonitorService
檔案監控服務，提供：
- 自動監控下載資料夾
- 新檔案自動處理
- 批次處理現有檔案

```typescript
import { fileMonitorService } from '../services/fileMonitor.service';

// 啟動監控
await fileMonitorService.startMonitoring();

// 處理現有檔案
await fileMonitorService.processExistingFiles();

// 停止監控
await fileMonitorService.stopMonitoring();
```

## 使用方式

### 1. 傳統方式（向後相容）
```typescript
// 原有的時間驅動方式仍然可用
const taskService = new TaskService('CLINICO');
await taskService.download();
```

### 2. 新的檔名驅動方式
```typescript
// 新的檔名驅動方式
const taskService = new TaskService('CLINICO');
await taskService.downloadWithFileNameDriven();
```

### 3. 自動監控方式
```typescript
import downloadJob from '../schedules/download.job';

const job = new downloadJob('CLINICO');

// 啟動檔案監控
await job.startFileMonitoring();

// 處理現有檔案
await job.processExistingFiles();
```

## 排程任務更新

### 新增的方法
- `runWithFileNameDriven()` - 檔名驅動的一般交易處理
- `foodRunWithFileNameDriven()` - 檔名驅動的健康食品交易處理
- `startFileMonitoring()` - 啟動檔案監控
- `stopFileMonitoring()` - 停止檔案監控
- `processExistingFiles()` - 處理現有檔案

### 使用範例
```typescript
import downloadJob from '../schedules/download.job';

const clinicoJob = new downloadJob('CLINICO');

// 使用新的檔名驅動方式
await clinicoJob.runWithFileNameDriven();
await clinicoJob.foodRunWithFileNameDriven();

// 或啟動自動監控
await clinicoJob.startFileMonitoring();
```

## 測試

### 執行測試
```bash
# 編譯 TypeScript
npm run build

# 執行測試
node dist/tests/fileNameDriven.test.js
```

### 測試內容
- 檔案名稱解析測試
- 策略工廠測試
- 檔案名稱產生測試
- 檔案監控狀態測試
- 模擬檔案處理流程

## 部署建議

### 階段性部署
1. **第一階段**：部署新程式碼，但繼續使用原有排程
2. **第二階段**：逐步切換到檔名驅動方式
3. **第三階段**：啟用自動檔案監控

### 監控要點
- 檔案處理成功率
- 檔案識別準確性
- 處理時間效能
- 錯誤日誌監控

## 故障排除

### 常見問題

1. **檔案格式無效**
   - 檢查檔案名稱是否符合 `F787$YYYYMMDD[0-9]{2}O` 格式
   - 確認日期格式正確

2. **通路識別錯誤**
   - 確認檔案名稱中的數字代碼正確
   - 檢查通路對應表

3. **檔案監控未啟動**
   - 確認下載資料夾存在
   - 檢查檔案權限
   - 查看監控服務狀態

### 日誌查看
```bash
# 查看檔案處理日誌
grep "File processed" logs/application.log

# 查看檔案監控日誌
grep "File monitoring" logs/application.log

# 查看錯誤日誌
grep "ERROR" logs/application.log
```

## 效能考量

### 優化建議
- 檔案監控使用適當的穩定性閾值
- 批次處理多個檔案時使用並行處理
- 定期清理已處理的檔案

### 資源使用
- 檔案監控服務佔用少量記憶體
- 檔案處理時 CPU 使用率會暫時增加
- 磁碟 I/O 主要發生在檔案讀取時

## 未來擴展

### 可能的改進
- 支援更多檔案格式
- 增加檔案處理優先級
- 實作檔案處理佇列
- 增加處理結果通知機制

### 架構擴展
- 支援分散式檔案處理
- 增加檔案處理狀態追蹤
- 實作檔案處理重試機制
