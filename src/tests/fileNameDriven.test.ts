import { FileNameParser, TransactionType } from '../services/parsers/fileName.parser';
import { FileProcessingStrategyFactory } from '../services/strategies/fileProcessing.strategy';
import { fileMonitorService } from '../services/fileMonitor.service';
import { CLINICO, SKD, IB } from '../const';
import logger from '../services/logger.service';

/**
 * 檔名驅動架構測試
 * 用於驗證新的檔案名稱解析和處理流程
 */
export class FileNameDrivenTest {
    
    /**
     * 測試檔案名稱解析器
     */
    static testFileNameParser(): void {
        console.log('=== 測試檔案名稱解析器 ===');
        
        const testCases = [
            // 有效的檔案名稱
            'F787$2024072601O', // CLINICO 每日交易
            'F787$2024072602O', // SKD 每日交易
            'F787$2024072603O', // IB 每日交易
            'F787$2024072604O', // CLINICO 補交易
            'F787$2024072605O', // SKD 補交易
            'F787$2024072606O', // IB 補交易
            'F787$2024072621O', // CLINICO 健康食品
            'F787$2024072622O', // SKD 健康食品
            'F787$2024072623O', // IB 健康食品
            
            // 無效的檔案名稱
            'F787$20240726999O', // 無效的交易類型代碼
            'F787$2024072O',     // 日期格式錯誤
            'G787$2024072601O',  // 錯誤的前綴
            'F787$2024072601X',  // 錯誤的後綴
            'F787$2024133101O',  // 無效日期（13月）
        ];

        testCases.forEach(fileName => {
            const result = FileNameParser.parse(fileName);
            console.log(`檔案: ${fileName}`);
            console.log(`  有效: ${result.isValid}`);
            if (result.isValid) {
                console.log(`  通路: ${result.company}`);
                console.log(`  交易類型: ${result.transactionType}`);
                console.log(`  日期: ${result.fileDate?.toISOString().split('T')[0]}`);
            } else {
                console.log(`  錯誤: ${result.errorMessage}`);
            }
            console.log('');
        });
    }

    /**
     * 測試策略工廠
     */
    static testStrategyFactory(): void {
        console.log('=== 測試策略工廠 ===');
        
        const companies = [CLINICO, SKD, IB];
        const transactionTypes = [TransactionType.DAILY, TransactionType.REPAY, TransactionType.FOOD];

        companies.forEach(company => {
            console.log(`通路: ${company}`);
            transactionTypes.forEach(transactionType => {
                try {
                    const strategy = FileProcessingStrategyFactory.createStrategy(transactionType, company);
                    console.log(`  ${transactionType}: 策略建立成功`);
                } catch (error) {
                    console.log(`  ${transactionType}: 策略建立失敗 - ${error.message}`);
                }
            });
            console.log('');
        });
    }

    /**
     * 測試檔案名稱產生
     */
    static testFileNameGeneration(): void {
        console.log('=== 測試檔案名稱產生 ===');
        
        const testDate = new Date(2024, 6, 26); // 2024-07-26
        const companies = [CLINICO, SKD, IB];
        const transactionTypes = [TransactionType.DAILY, TransactionType.REPAY, TransactionType.FOOD];

        companies.forEach(company => {
            console.log(`通路: ${company}`);
            transactionTypes.forEach(transactionType => {
                const fileName = FileNameParser.generateFileName(company, transactionType, testDate);
                console.log(`  ${transactionType}: ${fileName}`);
            });
            console.log('');
        });
    }

    /**
     * 測試檔案監控服務狀態
     */
    static testFileMonitorStatus(): void {
        console.log('=== 測試檔案監控服務狀態 ===');
        
        const status = fileMonitorService.getMonitoringStatus();
        console.log(`監控中: ${status.isMonitoring}`);
        console.log(`監控資料夾數量: ${status.activeWatchers}`);
        console.log(`監控資料夾:`);
        status.watchedFolders.forEach(folder => {
            console.log(`  - ${folder}`);
        });
    }

    /**
     * 測試支援的通路和交易類型
     */
    static testSupportedTypes(): void {
        console.log('=== 測試支援的通路和交易類型 ===');
        
        const companies = FileNameParser.getSupportedCompanies();
        console.log(`支援的通路: ${companies.join(', ')}`);
        
        companies.forEach(company => {
            const types = FileNameParser.getSupportedTransactionTypes(company);
            console.log(`${company} 支援的交易類型: ${types.join(', ')}`);
        });
    }

    /**
     * 測試檔案類型代碼對應
     */
    static testTypeCodeMapping(): void {
        console.log('=== 測試檔案類型代碼對應 ===');
        
        const mappings = [
            { company: CLINICO, type: TransactionType.DAILY, expectedCode: '01' },
            { company: CLINICO, type: TransactionType.REPAY, expectedCode: '04' },
            { company: CLINICO, type: TransactionType.FOOD, expectedCode: '21' },
            { company: SKD, type: TransactionType.DAILY, expectedCode: '02' },
            { company: SKD, type: TransactionType.REPAY, expectedCode: '05' },
            { company: SKD, type: TransactionType.FOOD, expectedCode: '22' },
            { company: IB, type: TransactionType.DAILY, expectedCode: '03' },
            { company: IB, type: TransactionType.REPAY, expectedCode: '06' },
            { company: IB, type: TransactionType.FOOD, expectedCode: '23' },
        ];

        mappings.forEach(mapping => {
            const actualCode = FileNameParser.getTypeCode(mapping.company, mapping.type);
            const isCorrect = actualCode === mapping.expectedCode;
            console.log(`${mapping.company} ${mapping.type}: ${actualCode} ${isCorrect ? '✓' : '✗'}`);
        });
    }

    /**
     * 執行所有測試
     */
    static runAllTests(): void {
        console.log('開始執行檔名驅動架構測試...\n');
        
        try {
            this.testFileNameParser();
            this.testStrategyFactory();
            this.testFileNameGeneration();
            this.testSupportedTypes();
            this.testTypeCodeMapping();
            this.testFileMonitorStatus();
            
            console.log('所有測試執行完成！');
        } catch (error) {
            console.error('測試執行過程中發生錯誤:', error.message);
        }
    }

    /**
     * 模擬檔案處理流程
     */
    static async simulateFileProcessing(): Promise<void> {
        console.log('=== 模擬檔案處理流程 ===');
        
        const testFiles = [
            'F787$2024072601O', // CLINICO 每日交易
            'F787$2024072622O', // SKD 健康食品
            'F787$2024072606O', // IB 補交易
        ];

        for (const fileName of testFiles) {
            console.log(`處理檔案: ${fileName}`);
            
            const parseResult = FileNameParser.parse(fileName);
            if (!parseResult.isValid) {
                console.log(`  檔案格式無效: ${parseResult.errorMessage}`);
                continue;
            }

            console.log(`  通路: ${parseResult.company}`);
            console.log(`  交易類型: ${parseResult.transactionType}`);
            console.log(`  日期: ${parseResult.fileDate?.toISOString().split('T')[0]}`);
            
            try {
                const strategy = FileProcessingStrategyFactory.createStrategy(
                    parseResult.transactionType!,
                    parseResult.company!
                );
                console.log(`  策略建立成功: ${strategy.constructor.name}`);
                
                // 注意：這裡不實際執行處理，只是模擬
                console.log(`  模擬處理完成`);
            } catch (error) {
                console.log(`  策略建立失敗: ${error.message}`);
            }
            
            console.log('');
        }
    }
}

// 如果直接執行此檔案，則運行測試
if (require.main === module) {
    FileNameDrivenTest.runAllTests();
    FileNameDrivenTest.simulateFileProcessing().then(() => {
        console.log('模擬測試完成');
    }).catch(error => {
        console.error('模擬測試失敗:', error.message);
    });
}
