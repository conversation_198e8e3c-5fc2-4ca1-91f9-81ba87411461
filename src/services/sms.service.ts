import db from '@clinico/clinico-persistence';
import { Utils } from '@clinico/clinico-node-framework';
import {
    IPayFailed,
    SendSMSParams,
    EnumSMSLogType,
} from './interfaces/sms.interface';
import { CLINICO, IB, SKD, payFailedMessageForSMS } from '../const';
import logger from './logger.service';
import { FormService } from './form.service';
import { ShipperType } from './interfaces/order.interface';
import { RepayService } from './repay.service';
import configs from '../configs'; // 匯入 configs
import * as moment from 'moment';
import { EnumPayFailedType } from './interfaces/repay.interface';

export class SMSService {
    private company: string;
    private formService: FormService;
    private repayService: RepayService;

    // 用於稽核測試，收集簡訊內容 (只有在稽核模式下才需要)
    public static collectedSmsMessages: {
        company: string;
        applicationId: string;
        smsContent: string;
        source: 'SCHEDULE' | 'ACH'; // 新增來源標識
    }[] = [];

    constructor(company: string) {
        this.company = company;
        this.formService = new FormService(this.company);
        this.repayService = new RepayService(this.company);
    }

    private async send(params: SendSMSParams): Promise<boolean> {
        let account: Utils.SMS.Accounts;
        switch (this.company) {
            case SKD: // SKD
                account = Utils.SMS.Accounts.SKD;
                break;
            case IB: // IB
                account = Utils.SMS.Accounts.IB;
                break;
            case CLINICO: // HA
            default:
                account = Utils.SMS.Accounts.HA;
                break;
        }

        try {
            const result = await Utils.SMS.sendAndReturnResult(params, account);

            if (result.status !== true) {
                throw new Error(`簡訊發送失敗: ${result?.error}`);
            }
            return result.status;
        } catch (err) {
            logger.error(`簡訊發送非預期錯誤: ${err.message}`);
        }

        return false;
    }

    async sendFailedToMember(
        params: IPayFailed,
        isRepay: boolean,
        source: 'SCHEDULE' | 'ACH' = 'SCHEDULE',
    ): Promise<number | undefined> {
        try {
            const {
                orderType,
                applicationId,
                period,
                errorCode,
                errorMessage,
                amount,
                method,
            } = params;

            const application = await this.formService.findOne(applicationId);

            if (!application) {
                logger.warn('application not found');
                return;
            }

            const { memberCode, mobilePhone, storeCode } = application;

            if (!mobilePhone) {
                logger.warn(`會員 ${memberCode} 沒有手機號碼，無法發送簡訊。`);
                return;
            }

            const failedType = await this.repayService.getPayFailedType(
                applicationId,
            );

            // 取得門市資訊
            const store = await db.Store.findOne({
                where: { code: storeCode },
            });

            const companyName = this.getCompanyName();
            const productType = await this.getProductTypeName(orderType);

            // 取得smsContent樣板
            let smsContent = '';
            const templatesForPeriodType = payFailedMessageForSMS[failedType];

            if (templatesForPeriodType) {
                let selectedTemplate:
                    | {
                          errorCode?: string[];
                          reason?: string;
                          orderType: ShipperType[] | string[];
                          template: string;
                      }
                    | undefined = undefined;

                // 如果不是補刷，優先找 errorCode 匹配的樣板
                if (isRepay) {
                    selectedTemplate = templatesForPeriodType.find(
                        (template) =>
                            template.reason === '補刷失敗' &&
                            template.orderType.includes(orderType),
                    );
                } else if (errorCode) {
                    selectedTemplate = templatesForPeriodType.find((template) =>
                        template.errorCode?.includes(errorCode) &&
                        source === 'ACH'
                            ? errorCode != '02' //ACH時 02 不是餘額不足
                            : true && template.orderType.includes(orderType),
                    );
                }

                // 如果不是補刷，沒有 errorCode 匹配，則尋找 reason 匹配的樣板
                if (!selectedTemplate && !isRepay) {
                    selectedTemplate = templatesForPeriodType.find(
                        (template) =>
                            (template.reason === '其他原因' ||
                                template.reason === '不分原因') &&
                            template.orderType.includes(orderType),
                    );
                }

                if (selectedTemplate && selectedTemplate.template) {
                    // 確保 template 存在
                    smsContent = selectedTemplate.template;
                    smsContent = smsContent.replace(
                        '{{companyName}}',
                        companyName,
                    );
                    smsContent = smsContent.replace(
                        '{{orderType}}',
                        productType,
                    );
                    smsContent = smsContent.replace(
                        '{{amount}}',
                        amount.toString(),
                    );
                    smsContent = smsContent.replace(
                        '{{storeName}}',
                        store?.name ?? '',
                    );
                    smsContent = smsContent.replace(
                        '{{storePhone}}',
                        store?.phone ?? '',
                    );
                    smsContent = smsContent.replace(
                        '{{retryDate}}',
                        moment().add(3, 'days').format('MM/DD'), // 發出日+3日, MM/DD
                    );
                    const serviceNumberMap: Record<string, string> = {
                        CLINICO: '0800551188',
                        SKD: '0800000576',
                        IB: '0800211430',
                    };
                    smsContent = smsContent.replace(
                        '{{serviceNumber}}',
                        serviceNumberMap[this.company] ?? '',
                    );
                    const methodMap: Record<string, string> = {
                        '1': '現金',
                        '2': '信用卡',
                        '3': '帳戶',
                    };
                    smsContent = smsContent.replace(
                        '{{method}}',
                        methodMap[method] ?? '交易',
                    );

                    if (failedType == EnumPayFailedType.ConsecutivePeriods2) {
                        const totalAmount = amount * 2;
                        smsContent = smsContent.replace(
                            '{{totalAmount}}',
                            totalAmount.toString(),
                        );
                    }
                    if (failedType == EnumPayFailedType.ConsecutivePeriods3) {
                        const totalAmount = amount * 3;
                        smsContent = smsContent.replace(
                            '{{totalAmount}}',
                            totalAmount.toString(),
                        );
                    }
                    if (failedType == EnumPayFailedType.ConsecutivePeriods4) {
                        const totalAmount = amount * 4;
                        smsContent = smsContent.replace(
                            '{{totalAmount}}',
                            totalAmount.toString(),
                        );
                    }
                } else {
                    logger.warn(
                        `找不到符合條件的簡訊樣板或樣板內容為空，period: ${period}, orderType: ${orderType}, errorMessage: ${errorMessage}`,
                    );
                    return;
                }
            } else {
                logger.warn(`找不到該期數的簡訊樣板，period: ${period}`);
                return;
            }

            // 根據配置決定是否實際發送簡訊
            if (configs.sms?.enableSending) {
                // const sendParams = {
                //     subject: '繳費通知', // 簡訊主旨
                //     content: smsContent,
                //     mobile: mobilePhone,
                //     type: EnumSMSLogType.VPOS,
                //     referenceCode: applicationId,
                // };
                // const result = await this.send(sendParams);
                // if (result) {
                //     logger.info(`成功發送繳費失敗簡訊給會員 ${memberCode}`);
                //     return 1; // 表示成功發送一則簡訊
                // } else {
                //     logger.error(`發送繳費失敗簡訊給會員 ${memberCode} 失敗`);
                //     return 0; // 表示發送失敗
                // }
            } else {
                // 稽核模式：收集簡訊內容
                SMSService.collectedSmsMessages.push({
                    company: this.company,
                    applicationId,
                    smsContent,
                    source,
                });

                logger.info(
                    `[稽核測試] 繳費失敗簡訊內容已收集 (訂單編號: ${applicationId})`,
                );
                return 1; // 表示成功收集簡訊內容
            }
        } catch (err) {
            logger.error(`處理繳費失敗簡訊發送時發生錯誤: ${err.message}`);
            return;
        }
    }

    private async getProductTypeName(orderType: ShipperType): Promise<string> {
        switch (orderType) {
            case ShipperType.HA:
                return '助聽器';
            case ShipperType.RS:
                return '呼吸器';
            case ShipperType.RSM:
                return '面罩';
            case ShipperType.FOOD:
                return '保健食品';
        }
    }

    private getCompanyName() {
        switch (this.company) {
            case CLINICO:
                return '科林';
            case SKD:
                return '濰樂';
            case IB:
                return '聽寶';
            default:
                return '';
        }
    }
}
