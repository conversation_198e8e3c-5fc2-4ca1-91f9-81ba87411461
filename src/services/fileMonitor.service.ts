import * as fs from 'fs';
import * as path from 'path';
import * as chokidar from 'chokidar';
import logger from './logger.service';
import configs from '../configs';
import { FileNameParser, FileNameParseResult } from './parsers/fileName.parser';
import { FileProcessingCoordinator } from './strategies/fileProcessing.strategy';
import { CLINICO, SKD, IB } from '../const';

/**
 * 檔案監控服務
 * 監控下載資料夾，當新檔案出現時自動根據檔案名稱觸發對應的處理流程
 */
export class FileMonitorService {
    private watchers: Map<string, chokidar.FSWatcher> = new Map();
    private coordinators: Map<string, FileProcessingCoordinator> = new Map();
    private isMonitoring: boolean = false;

    constructor() {
        this.initializeCoordinators();
    }

    /**
     * 初始化各通路的檔案處理協調器
     */
    private initializeCoordinators(): void {
        const companies = [CLINICO, SKD, IB];
        for (const company of companies) {
            this.coordinators.set(company, new FileProcessingCoordinator(company));
        }
    }

    /**
     * 開始監控所有通路的下載資料夾
     */
    async startMonitoring(): Promise<void> {
        if (this.isMonitoring) {
            logger.warn('File monitoring is already running');
            return;
        }

        logger.info('Starting file monitoring service...');
        
        const companies = [CLINICO, SKD, IB];
        
        for (const company of companies) {
            await this.startMonitoringCompany(company);
        }

        this.isMonitoring = true;
        logger.info('File monitoring service started successfully');
    }

    /**
     * 開始監控特定通路的下載資料夾
     */
    private async startMonitoringCompany(company: string): Promise<void> {
        const downloadFolder = `${configs.esun.downloadFolder}/${company}`;
        
        // 確保資料夾存在
        if (!fs.existsSync(downloadFolder)) {
            logger.warn(`Download folder does not exist: ${downloadFolder}`);
            return;
        }

        // 建立檔案監控器
        const watcher = chokidar.watch(downloadFolder, {
            ignored: /(^|[\/\\])\../, // 忽略隱藏檔案
            persistent: true,
            ignoreInitial: true, // 忽略初始檔案，只監控新增的檔案
            awaitWriteFinish: {
                stabilityThreshold: 2000, // 檔案穩定 2 秒後才處理
                pollInterval: 100
            }
        });

        // 監聽檔案新增事件
        watcher.on('add', async (filePath: string) => {
            await this.handleNewFile(company, filePath);
        });

        // 監聽錯誤事件
        watcher.on('error', (error: Error) => {
            logger.error(`File watcher error for ${company}: ${error.message}`);
        });

        this.watchers.set(company, watcher);
        logger.info(`Started monitoring ${company} download folder: ${downloadFolder}`);
    }

    /**
     * 處理新檔案
     */
    private async handleNewFile(company: string, filePath: string): Promise<void> {
        try {
            const fileName = path.basename(filePath);
            logger.info(`[${company}] New file detected: ${fileName}`);

            // 解析檔案名稱
            const parseResult = FileNameParser.parse(fileName);
            
            if (!parseResult.isValid) {
                logger.warn(`[${company}] Invalid file format: ${fileName} - ${parseResult.errorMessage}`);
                return;
            }

            // 檢查檔案是否屬於當前通路
            if (parseResult.company !== company) {
                logger.warn(`[${company}] File belongs to different company: ${fileName} (${parseResult.company})`);
                return;
            }

            // 取得對應的處理協調器
            const coordinator = this.coordinators.get(company);
            if (!coordinator) {
                logger.error(`[${company}] No coordinator found for company`);
                return;
            }

            // 處理檔案
            const result = await coordinator.processFile(parseResult, filePath);
            
            if (result.success) {
                logger.info(`[${company}] File processed successfully: ${fileName} - ${result.message}`);
            } else {
                logger.error(`[${company}] File processing failed: ${fileName} - ${result.errorMessage}`);
            }

        } catch (error) {
            logger.error(`[${company}] Error handling new file ${filePath}: ${error.message}`);
        }
    }

    /**
     * 停止監控
     */
    async stopMonitoring(): Promise<void> {
        if (!this.isMonitoring) {
            logger.warn('File monitoring is not running');
            return;
        }

        logger.info('Stopping file monitoring service...');

        // 關閉所有監控器
        for (const [company, watcher] of this.watchers.entries()) {
            await watcher.close();
            logger.info(`Stopped monitoring ${company} download folder`);
        }

        this.watchers.clear();
        this.isMonitoring = false;
        logger.info('File monitoring service stopped');
    }

    /**
     * 手動處理現有檔案
     * 用於處理監控啟動前就存在的檔案
     */
    async processExistingFiles(): Promise<void> {
        logger.info('Processing existing files...');

        const companies = [CLINICO, SKD, IB];
        
        for (const company of companies) {
            await this.processExistingFilesForCompany(company);
        }

        logger.info('Finished processing existing files');
    }

    /**
     * 處理特定通路的現有檔案
     */
    private async processExistingFilesForCompany(company: string): Promise<void> {
        try {
            const downloadFolder = `${configs.esun.downloadFolder}/${company}`;
            
            if (!fs.existsSync(downloadFolder)) {
                logger.warn(`Download folder does not exist: ${downloadFolder}`);
                return;
            }

            const files = fs.readdirSync(downloadFolder);
            const coordinator = this.coordinators.get(company);
            
            if (!coordinator) {
                logger.error(`[${company}] No coordinator found for company`);
                return;
            }

            for (const fileName of files) {
                const filePath = path.join(downloadFolder, fileName);
                const stats = fs.statSync(filePath);
                
                // 只處理檔案，不處理資料夾
                if (!stats.isFile()) {
                    continue;
                }

                const parseResult = FileNameParser.parse(fileName);
                
                if (!parseResult.isValid || parseResult.company !== company) {
                    continue;
                }

                logger.info(`[${company}] Processing existing file: ${fileName}`);
                
                const result = await coordinator.processFile(parseResult, filePath);
                
                if (result.success) {
                    logger.info(`[${company}] Existing file processed successfully: ${fileName}`);
                } else {
                    logger.error(`[${company}] Existing file processing failed: ${fileName} - ${result.errorMessage}`);
                }
            }

        } catch (error) {
            logger.error(`[${company}] Error processing existing files: ${error.message}`);
        }
    }

    /**
     * 取得監控狀態
     */
    getMonitoringStatus(): {
        isMonitoring: boolean;
        watchedFolders: string[];
        activeWatchers: number;
    } {
        const watchedFolders: string[] = [];
        
        for (const company of this.watchers.keys()) {
            watchedFolders.push(`${configs.esun.downloadFolder}/${company}`);
        }

        return {
            isMonitoring: this.isMonitoring,
            watchedFolders,
            activeWatchers: this.watchers.size
        };
    }

    /**
     * 重新啟動監控
     */
    async restartMonitoring(): Promise<void> {
        await this.stopMonitoring();
        await this.startMonitoring();
    }
}

// 建立單例實例
export const fileMonitorService = new FileMonitorService();
