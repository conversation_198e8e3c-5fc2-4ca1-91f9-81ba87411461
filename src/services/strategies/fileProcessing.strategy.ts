import { FileNameParseResult, TransactionType } from '../parsers/fileName.parser';
import { EsunService } from '../esun.service';
import { TaskService } from '../task.service';
import { FoodTaskService } from '../foodTask.service';
import logger from '../logger.service';

/**
 * 檔案處理結果介面
 */
export interface FileProcessingResult {
    /** 是否處理成功 */
    success: boolean;
    /** 處理的檔案數量 */
    processedFileCount: number;
    /** 處理訊息 */
    message: string;
    /** 錯誤訊息 */
    errorMessage?: string;
}

/**
 * 檔案處理策略介面
 */
export interface IFileProcessingStrategy {
    /**
     * 處理檔案
     * @param parseResult - 檔案解析結果
     * @param filePath - 檔案路徑
     * @returns 處理結果
     */
    process(parseResult: FileNameParseResult, filePath: string): Promise<FileProcessingResult>;
}

/**
 * 每日交易檔案處理策略
 */
export class DailyTransactionStrategy implements IFileProcessingStrategy {
    constructor(private company: string) {}

    async process(parseResult: FileNameParseResult, filePath: string): Promise<FileProcessingResult> {
        try {
            logger.info(`[${this.company}] 開始處理每日交易檔案: ${parseResult.originalFileName}`);
            
            const taskService = new TaskService(this.company, parseResult.fileDate);
            
            // 使用檔案日期來處理回應
            const responseResult = await taskService['generateResponse']({ isRepay: false });
            
            return {
                success: true,
                processedFileCount: 1,
                message: `每日交易檔案處理完成: ${parseResult.originalFileName}`
            };
        } catch (error) {
            logger.error(`[${this.company}] 每日交易檔案處理失敗: ${error.message}`);
            return {
                success: false,
                processedFileCount: 0,
                message: `每日交易檔案處理失敗`,
                errorMessage: error.message
            };
        }
    }
}

/**
 * 補交易檔案處理策略
 */
export class RepayTransactionStrategy implements IFileProcessingStrategy {
    constructor(private company: string) {}

    async process(parseResult: FileNameParseResult, filePath: string): Promise<FileProcessingResult> {
        try {
            logger.info(`[${this.company}] 開始處理補交易檔案: ${parseResult.originalFileName}`);
            
            const taskService = new TaskService(this.company, parseResult.fileDate);
            
            // 使用檔案日期來處理補刷回應
            const responseResult = await taskService['generateResponse']({ isRepay: true });
            
            return {
                success: true,
                processedFileCount: 1,
                message: `補交易檔案處理完成: ${parseResult.originalFileName}`
            };
        } catch (error) {
            logger.error(`[${this.company}] 補交易檔案處理失敗: ${error.message}`);
            return {
                success: false,
                processedFileCount: 0,
                message: `補交易檔案處理失敗`,
                errorMessage: error.message
            };
        }
    }
}

/**
 * 健康食品交易檔案處理策略
 */
export class FoodTransactionStrategy implements IFileProcessingStrategy {
    constructor(private company: string) {}

    async process(parseResult: FileNameParseResult, filePath: string): Promise<FileProcessingResult> {
        try {
            logger.info(`[${this.company}] 開始處理健康食品交易檔案: ${parseResult.originalFileName}`);
            
            const foodTaskService = new FoodTaskService(this.company, parseResult.fileDate);
            
            // 使用檔案日期來處理食品交易回應
            const responseResult = await foodTaskService.generateResponse({ isRepay: false });
            
            return {
                success: true,
                processedFileCount: 1,
                message: `健康食品交易檔案處理完成: ${parseResult.originalFileName}`
            };
        } catch (error) {
            logger.error(`[${this.company}] 健康食品交易檔案處理失敗: ${error.message}`);
            return {
                success: false,
                processedFileCount: 0,
                message: `健康食品交易檔案處理失敗`,
                errorMessage: error.message
            };
        }
    }
}

/**
 * 檔案處理策略工廠
 */
export class FileProcessingStrategyFactory {
    /**
     * 根據交易類型建立對應的處理策略
     * @param transactionType - 交易類型
     * @param company - 通路
     * @returns 處理策略
     */
    static createStrategy(transactionType: TransactionType, company: string): IFileProcessingStrategy {
        switch (transactionType) {
            case TransactionType.DAILY:
                return new DailyTransactionStrategy(company);
            case TransactionType.REPAY:
                return new RepayTransactionStrategy(company);
            case TransactionType.FOOD:
                return new FoodTransactionStrategy(company);
            default:
                throw new Error(`不支援的交易類型: ${transactionType}`);
        }
    }
}

/**
 * 檔案處理協調器
 * 負責協調檔案解析和策略執行
 */
export class FileProcessingCoordinator {
    constructor(private company: string) {}

    /**
     * 處理單一檔案
     * @param parseResult - 檔案解析結果
     * @param filePath - 檔案路徑
     * @returns 處理結果
     */
    async processFile(parseResult: FileNameParseResult, filePath: string): Promise<FileProcessingResult> {
        if (!parseResult.isValid) {
            return {
                success: false,
                processedFileCount: 0,
                message: `檔案格式無效`,
                errorMessage: parseResult.errorMessage
            };
        }

        // 檢查檔案是否屬於當前通路
        if (parseResult.company !== this.company) {
            return {
                success: false,
                processedFileCount: 0,
                message: `檔案不屬於當前通路`,
                errorMessage: `檔案通路: ${parseResult.company}, 當前通路: ${this.company}`
            };
        }

        try {
            const strategy = FileProcessingStrategyFactory.createStrategy(
                parseResult.transactionType!,
                this.company
            );
            
            return await strategy.process(parseResult, filePath);
        } catch (error) {
            logger.error(`[${this.company}] 檔案處理協調失敗: ${error.message}`);
            return {
                success: false,
                processedFileCount: 0,
                message: `檔案處理協調失敗`,
                errorMessage: error.message
            };
        }
    }

    /**
     * 批次處理多個檔案
     * @param parseResults - 檔案解析結果列表
     * @param filePaths - 檔案路徑列表
     * @returns 處理結果
     */
    async processFiles(
        parseResults: FileNameParseResult[], 
        filePaths: string[]
    ): Promise<FileProcessingResult> {
        if (parseResults.length !== filePaths.length) {
            throw new Error('檔案解析結果和檔案路徑數量不一致');
        }

        let totalProcessed = 0;
        let totalSuccess = 0;
        const errors: string[] = [];

        for (let i = 0; i < parseResults.length; i++) {
            const result = await this.processFile(parseResults[i], filePaths[i]);
            totalProcessed++;
            
            if (result.success) {
                totalSuccess++;
            } else {
                errors.push(`${parseResults[i].originalFileName}: ${result.errorMessage}`);
            }
        }

        return {
            success: totalSuccess === totalProcessed,
            processedFileCount: totalSuccess,
            message: `批次處理完成: 成功 ${totalSuccess}/${totalProcessed}`,
            errorMessage: errors.length > 0 ? errors.join('; ') : undefined
        };
    }
}
