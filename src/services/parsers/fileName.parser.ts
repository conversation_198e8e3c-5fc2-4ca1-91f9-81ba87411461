import { CLINICO, SKD, IB } from '../../const';

/**
 * 檔案名稱解析結果介面
 */
export interface FileNameParseResult {
    /** 是否為有效的檔案名稱格式 */
    isValid: boolean;
    /** 通路類型 */
    company?: string;
    /** 交易類型 */
    transactionType?: TransactionType;
    /** 檔案日期 */
    fileDate?: Date;
    /** 原始檔案名稱 */
    originalFileName: string;
    /** 錯誤訊息 */
    errorMessage?: string;
}

/**
 * 交易類型列舉
 */
export enum TransactionType {
    /** 每日交易 */
    DAILY = 'DAILY',
    /** 補交易 */
    REPAY = 'REPAY',
    /** 健康食品交易 */
    FOOD = 'FOOD'
}

/**
 * 檔案名稱解析器
 * 解析格式: F787$YYYYMMDD[0-9]{2}O
 * 
 * 通路和交易類型對應：
 * CLINICO: 01(每日), 04(補交易), 21(健康食品)
 * SKD: 02(每日), 05(補交易), 22(健康食品)
 * IB: 03(每日), 06(補交易), 23(健康食品)
 */
export class FileNameParser {
    /** 檔案名稱正則表達式 */
    private static readonly FILE_NAME_REGEX = /^F787\$(\d{8})(\d{2})O$/;
    
    /** 通路和交易類型對應表 */
    private static readonly COMPANY_TRANSACTION_MAP = new Map<string, { company: string; transactionType: TransactionType }>([
        // CLINICO
        ['01', { company: CLINICO, transactionType: TransactionType.DAILY }],
        ['04', { company: CLINICO, transactionType: TransactionType.REPAY }],
        ['21', { company: CLINICO, transactionType: TransactionType.FOOD }],
        
        // SKD
        ['02', { company: SKD, transactionType: TransactionType.DAILY }],
        ['05', { company: SKD, transactionType: TransactionType.REPAY }],
        ['22', { company: SKD, transactionType: TransactionType.FOOD }],
        
        // IB
        ['03', { company: IB, transactionType: TransactionType.DAILY }],
        ['06', { company: IB, transactionType: TransactionType.REPAY }],
        ['23', { company: IB, transactionType: TransactionType.FOOD }]
    ]);

    /**
     * 解析檔案名稱
     * @param fileName - 檔案名稱
     * @returns 解析結果
     */
    static parse(fileName: string): FileNameParseResult {
        const result: FileNameParseResult = {
            isValid: false,
            originalFileName: fileName
        };

        // 移除副檔名
        const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
        
        // 檢查檔案名稱格式
        const match = this.FILE_NAME_REGEX.exec(nameWithoutExt);
        if (!match) {
            result.errorMessage = `檔案名稱格式不符合 F787$YYYYMMDD[0-9]{2}O 格式: ${fileName}`;
            return result;
        }

        const [, dateStr, typeCode] = match;

        // 解析日期
        try {
            const year = parseInt(dateStr.substring(0, 4));
            const month = parseInt(dateStr.substring(4, 6)) - 1; // JavaScript 月份從 0 開始
            const day = parseInt(dateStr.substring(6, 8));
            
            const fileDate = new Date(year, month, day);
            
            // 驗證日期有效性
            if (fileDate.getFullYear() !== year || 
                fileDate.getMonth() !== month || 
                fileDate.getDate() !== day) {
                result.errorMessage = `無效的日期格式: ${dateStr}`;
                return result;
            }
            
            result.fileDate = fileDate;
        } catch (error) {
            result.errorMessage = `日期解析失敗: ${dateStr}`;
            return result;
        }

        // 解析通路和交易類型
        const companyTransaction = this.COMPANY_TRANSACTION_MAP.get(typeCode);
        if (!companyTransaction) {
            result.errorMessage = `未知的交易類型代碼: ${typeCode}`;
            return result;
        }

        result.company = companyTransaction.company;
        result.transactionType = companyTransaction.transactionType;
        result.isValid = true;

        return result;
    }

    /**
     * 檢查檔案是否為有效的玉山回應檔案
     * @param fileName - 檔案名稱
     * @returns 是否為有效檔案
     */
    static isValidEsunResponseFile(fileName: string): boolean {
        const parseResult = this.parse(fileName);
        return parseResult.isValid;
    }

    /**
     * 根據通路和交易類型取得對應的檔案類型代碼
     * @param company - 通路
     * @param transactionType - 交易類型
     * @returns 檔案類型代碼
     */
    static getTypeCode(company: string, transactionType: TransactionType): string | null {
        for (const [code, mapping] of this.COMPANY_TRANSACTION_MAP.entries()) {
            if (mapping.company === company && mapping.transactionType === transactionType) {
                return code;
            }
        }
        return null;
    }

    /**
     * 取得所有支援的通路列表
     * @returns 通路列表
     */
    static getSupportedCompanies(): string[] {
        const companies = new Set<string>();
        for (const mapping of this.COMPANY_TRANSACTION_MAP.values()) {
            companies.add(mapping.company);
        }
        return Array.from(companies);
    }

    /**
     * 取得指定通路支援的交易類型
     * @param company - 通路
     * @returns 交易類型列表
     */
    static getSupportedTransactionTypes(company: string): TransactionType[] {
        const types: TransactionType[] = [];
        for (const mapping of this.COMPANY_TRANSACTION_MAP.values()) {
            if (mapping.company === company) {
                types.push(mapping.transactionType);
            }
        }
        return types;
    }

    /**
     * 產生檔案名稱
     * @param company - 通路
     * @param transactionType - 交易類型
     * @param date - 日期
     * @returns 檔案名稱（不含副檔名）
     */
    static generateFileName(company: string, transactionType: TransactionType, date: Date): string | null {
        const typeCode = this.getTypeCode(company, transactionType);
        if (!typeCode) {
            return null;
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const dateStr = `${year}${month}${day}`;

        return `F787$${dateStr}${typeCode}O`;
    }
}
