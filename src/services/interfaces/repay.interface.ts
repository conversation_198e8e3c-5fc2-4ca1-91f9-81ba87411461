import { EnumPayStateType } from './common.interface';
import { Order } from './order.interface';

export interface RepayOrder extends Order {
    askingId: number;
}

export interface WPCRepayFee {
    APPLICATION_CODE: string;
    SHIP_CODE: string;
    PERIOD: number;
    FEE_DATE: Date;
    FEE: number;
    STATE: string;
    METHOD: string;
    DISCOUNT_CODE: string;
    MEMO: string;
    ACTIVE: string;
    CREATE_DATE: Date;
    CREATE_TIME: string;
    MODIFY_DATE: Date;
    STORE_CODE: string;
    USER_CODE: string;
    LEGAL: string;
}

export interface ISearchRepay {
    shipId?: string;
    applicationId?: string;
    state?: EnumPayStateType;
    feeDate?: Date;
    isCard?: boolean;
    period?: number;
    isACH?: boolean;
    isProcess?: boolean;
    betweenDate?: {
        date1: string;
        date2: string;
    };
    beforeDate?: Date;
}

export interface ICreateRepayParams {
    applicationCode: string;
    shipCode?: string;
    period: number;
    fee: number;
    method: string;
    discountCode?: string;
    memo?: string;
    storeCode: string;
    userCode?: string;
    legal: string;
}

export interface IUpdateRepayParams {
    filters: {
        applicationCode: string;
        period: number;
    };
    askingId: number;
}

export interface IUpdateRepayProcessParams {
    filters: {
        applicationCode: string;
        period: number;
    };
}

export enum EnumPayFailedType {
    Normal = 'Normal',
    ConsecutivePeriods2 = 'ConsecutivePeriods2',
    ConsecutivePeriods3 = 'ConsecutivePeriods3',
    ConsecutivePeriods4 = 'ConsecutivePeriods4',
}
